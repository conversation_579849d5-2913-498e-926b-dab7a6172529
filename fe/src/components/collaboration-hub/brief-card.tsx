import { <PERSON><PERSON><PERSON>, Eye, <PERSON>, <PERSON>, Calendar, MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { useTranslations } from "@/lib/i18n/typed-translations"
import type { CollaborationBriefListItemDto } from '@/lib/types/api.ts';

interface BriefCardProps {
  brief: CollaborationBriefListItemDto
  onEdit: (briefId: number) => void
  onDelete: (briefId: number) => void
}

export function BriefCard({ brief, onEdit, onDelete }: BriefCardProps) {
  const { t, keys } = useTranslations()

  const getAccessLevelIcon = (scope: string) => {
    switch (scope) {
      case "admins_only": return <Lock className="h-4 w-4 text-red-500" />
      case "admins_reviewers": return <Eye className="h-4 w-4 text-orange-500" />
      case "custom_selection": return <Users className="h-4 w-4 text-blue-500" />
      default: return <BookOpen className="h-4 w-4 text-green-500" />
    }
  }

  const getAccessLevelLabel = (scope: string) => {
    switch (scope) {
      case "admins_only": return t(keys.collaborationHubs.briefs.scopes.adminsOnly)
      case "admins_reviewers": return t(keys.collaborationHubs.briefs.scopes.adminsReviewers)
      case "custom_selection": return t(keys.collaborationHubs.briefs.scopes.customSelection)
      default: return t(keys.collaborationHubs.briefs.scopes.allParticipants)
    }
  }

  const getAccessLevelColor = (scope: string) => {
    switch (scope) {
      case "admins_only": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "admins_reviewers": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
      case "custom_selection": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      default: return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BookOpen className="h-5 w-5 text-primary" />
            </div>
            <div className="space-y-2 flex-1">
              <h3 className="font-semibold leading-tight">{brief.title}</h3>
              <div className="flex items-center gap-2">
                {getAccessLevelIcon(brief.scope)}
                <Badge className={getAccessLevelColor(brief.scope)}>
                  {getAccessLevelLabel(brief.scope)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Actions Menu - Always visible */}
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(brief.id)}>
                <Edit className="h-4 w-4 mr-2" />
                {t(keys.collaborationHubs.briefs.actions.edit)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(brief.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t(keys.collaborationHubs.briefs.actions.delete)}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {brief.bodyPreview || "No content provided."}
        </p>

        {/* Specific Participants (for custom selection) */}
        {brief.scope === "custom_selection" && brief.specificParticipantIds && brief.specificParticipantIds.length > 0 && (
          <div className="flex flex-wrap gap-1">
            <Badge variant="outline" className="text-xs">
              {brief.specificParticipantIds.length} specific participant{brief.specificParticipantIds.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        )}

        {/* Author and dates */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {getInitials(brief.createdByParticipantName || "Unknown")}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs text-muted-foreground">
              {brief.createdByParticipantName || "Unknown"}
            </span>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>
              {brief.updatedAt !== brief.createdAt
                ? `${t(keys.collaborationHubs.briefs.updated)} ${formatDate(brief.updatedAt)}`
                : `${t(keys.collaborationHubs.briefs.created)} ${formatDate(brief.createdAt)}`
              }
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
