/**
 * Posts hooks for the Collaboration Hub frontend.
 *
 * These hooks provide a clean interface for post CRUD operations
 * using the openapi-react-query pattern with proper TypeScript typing.
 *
 * Usage:
 * - usePosts: Fetch paginated posts with filtering
 * - usePostsInfinite: Fetch posts with infinite scrolling
 * - usePost: Fetch a specific post by ID
 * - useCreatePost: Create a new post
 * - useUpdatePost: Update an existing post
 * - useDeletePost: Delete a post
 * - useUploadMedia: Upload media files for posts (direct upload)
 * - usePresignedUpload: Upload media files using presigned URLs (concurrent)
 * - useHubParticipants: Fetch hub participants (for reviewer selection)
 * - useSubmitReview: Submit or update a post review
 *
 * All hooks include proper error handling, loading states, and automatic
 * multi-tenancy scoping handled by the backend.
 */

export { usePosts } from './use-posts';
export { usePostsInfinite } from './use-posts-infinite';
export { usePost } from './use-post';
export { useCreatePost } from './use-create-post';
export { useUpdatePost } from './use-update-post';
export { useDeletePost } from './use-delete-post';
export { useUploadMedia } from './use-upload-media';
export { usePresignedUpload } from './use-presigned-upload';
export { useHubParticipants } from './use-hub-participants';
export { useSubmitReview } from './use-submit-review';
