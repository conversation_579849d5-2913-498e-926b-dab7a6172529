import { $api } from '@/lib/api/client';

/**
 * Custom hook for fetching chat channels for a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * The backend automatically:
 * - Scopes channels to the current account (multi-tenancy)
 * - Filters channels based on user permissions
 * - Returns channels with participant counts and last messages
 * - Includes unread message counts
 * 
 * @param hubId - Hub ID to fetch channels for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannels(
  hubId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats', {
    params: {
      path: { hubId },
      query: {
        page: 0,
        size: 50, // Most hubs won't have more than 50 channels
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 30 seconds (channels change less frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for fresh unread counts
    refetchOnWindowFocus: true,
  });
}

/**
 * Custom hook for fetching a specific chat channel details.
 * 
 * @param hubId - Hub ID
 * @param channelId - Channel ID to fetch details for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannel(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats/{channelId}', {
    params: {
      path: { hubId, channelId },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
  });
}
