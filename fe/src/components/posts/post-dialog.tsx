import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Trash2 } from "lucide-react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { MediaCarousel } from "@/components/media/media-carousel"
import { CommentsSection } from "./comments-section"
import { PostReviewForm } from "./post-review-form"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { usePost } from "@/hooks/posts"
import { useDeletePost } from "@/hooks/posts"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import type { MediaItem } from "@/lib/types/api"
import { cn } from '@/lib/utils.ts';
import { useIsMobile } from '@/hooks/use-mobile.ts';

interface PostDialogProps {
  postId: number
  hubId: number
  open: boolean
  onOpenChange: (open: boolean) => void
  onEdit?: (postId: number) => void
}

export function PostDialog({ postId, hubId, open, onOpenChange, onEdit }: PostDialogProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const { data: post, isLoading, error } = usePost(postId, { enabled: open })
  const deletePost = useDeletePost()

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatRelativeTime = (dateString?: string) => {
    if (!dateString) return ''
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return ''
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200'
      case 'needs_rework': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'pending': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const highlightHashtagsAndMentions = (text: string) => {
    return text
      .replace(/\n/g, '<br>')
      .replace(
        /(#[^\s]+|@[^\s]+)/g,
        '<span class="text-blue-600 hover:text-blue-700 font-medium cursor-pointer">$1</span>'
      )
  }

  const handleEdit = () => {
    if (post?.id) {
      onEdit?.(post.id)
      onOpenChange(false)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!post?.id) return

    try {
      await deletePost.mutateAsync({ params: { path: { postId: post.id } } })
      toast.success(t(keys.collaborationHubs.posts.postDeleted))
      setDeleteDialogOpen(false)
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to delete post:', error)
      toast.error(t(keys.collaborationHubs.posts.failedToDelete))
    }
  }

  // Convert API MediaItem to MediaCarousel format
  const convertMediaItems = (mediaItems?: MediaItem[]) => {
    if (!mediaItems || mediaItems.length === 0) return []

    return mediaItems.map((item, index) => ({
      id: `media-${index}`,
      type: (item.type === 'video' ? 'video' : 'image') as 'image' | 'video',
      url: item.url || '',
      alt: `Media ${index + 1}`
    }))
  }

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <div className="flex items-center justify-center py-8">
            <p className="text-muted-foreground">Failed to load post</p>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className={cn(
          "max-w-4xl",
          isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
        )}>
          <DialogHeader className={cn(
            isMobile && "pb-2 pt-4 px-4"
          )}>
            <DialogTitle className={cn(
              "text-lg font-semibold",
              isMobile && "text-base"
            )}>
              {t(keys.collaborationHubs.posts.title)}
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className={cn(
            "mt-4",
            isMobile ? "h-[calc(100dvh-80px)] px-4" : "max-h-[75vh]"
          )}>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : post ? (
              <div className={cn(
                "space-y-6",
                isMobile && "pb-4"
              )}>
                  {/* Post Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={undefined} />
                        <AvatarFallback>
                          {getInitials(post.creator?.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{post.creator?.name || 'Unknown'}</span>
                          {post.review_status && (
                            <Badge variant="outline" className={getStatusColor(post.review_status)}>
                              {post.review_status.replace('_', ' ')}
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {formatRelativeTime(post.created_at)}
                        </p>
                      </div>
                    </div>

                    {/* Actions Menu */}
                    {(post.permissions?.can_edit) && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {post.permissions?.can_edit && (
                            <DropdownMenuItem onClick={handleEdit}>
                              <Edit className="h-4 w-4 mr-2" />
                              {t(keys.collaborationHubs.posts.actions.edit)}
                            </DropdownMenuItem>
                          )}
                          {post.permissions?.can_edit && (
                            <DropdownMenuItem
                              onClick={() => setDeleteDialogOpen(true)}
                              className="text-destructive focus:text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t(keys.collaborationHubs.posts.actions.delete)}
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>

                  {/* Media */}
                  {post.media_uris && post.media_uris.length > 0 && (
                    <div className="rounded-lg overflow-hidden">
                      <MediaCarousel
                        media={convertMediaItems(post.media_uris)}
                        hubId={hubId}
                        postId={post.id}
                        className="h-[400px] md:h-[500px] lg:h-[600px]"
                        enableRetry={true}
                      />
                    </div>
                  )}

                  {/* Caption */}
                  {post.caption && (
                    <div
                      className="text-sm leading-relaxed"
                      dangerouslySetInnerHTML={{
                        __html: highlightHashtagsAndMentions(post.caption)
                      }}
                    />
                  )}

                  {/* Reviewer Notes */}
                  {post.reviewer_notes && (
                    <div className="bg-muted/50 rounded-lg p-4">
                      <h4 className="font-medium text-sm mb-2">Notes for Reviewers</h4>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{post.reviewer_notes}</p>
                    </div>
                  )}

                  {/* Assigned Reviewers */}
                  {post.assigned_reviewers && post.assigned_reviewers.length > 0 && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium text-sm mb-3">Assigned Reviewers</h4>
                      <div className="space-y-2">
                        {post.assigned_reviewers.map((reviewer) => (
                          <div key={reviewer.id} className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback className="text-xs">
                                {getInitials(reviewer.name)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <p className="text-sm font-medium">{reviewer.name}</p>
                              <p className="text-xs text-muted-foreground">{reviewer.email}</p>
                            </div>
                            {reviewer.status && (
                              <Badge variant="outline" className={getStatusColor(reviewer.status)}>
                                {reviewer.status.replace('_', ' ')}
                              </Badge>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Post Review Form */}
                  {post.permissions.can_review && (
                    <PostReviewForm post={post} />
                  )}

                  {/* Comments Section */}
                  <CommentsSection
                    postId={postId}
                    hubId={hubId}
                    commentCount={0}
                    canComment={post.permissions?.can_comment || false}
                    className="border-t pt-6"
                    defaultExpanded={true}
                  />
              </div>
            ) : null}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.posts.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.posts.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.collaborationHubs.posts.form.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteConfirm}
              disabled={deletePost.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletePost.isPending ? t(keys.collaborationHubs.posts.deleting) : t(keys.collaborationHubs.posts.actions.delete)}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
