// React import removed - not needed with new JSX transform
import { Hash, Lock, Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useChatChannels } from '@/hooks/chat';
import { cn } from '@/lib/utils';

interface ChannelListProps {
  hubId: number;
  selectedChannelId?: number;
  onChannelSelect: (channelId: number) => void;
  className?: string;
}

export function ChannelList({ hubId, selectedChannelId, onChannelSelect, className }: ChannelListProps) {
  const { t, keys } = useTranslations();
  
  const {
    data: channelsResponse,
    isLoading,
    error,
  } = useChatChannels(hubId);

  const channels = channelsResponse?.content || [];

  const getChannelIcon = (scope: string) => {
    switch (scope) {
      case 'admins': return <Lock className="h-4 w-4" />;
      case 'creator_specific': return <Users className="h-4 w-4" />;
      default: return <Hash className="h-4 w-4" />;
    }
  };

  const getChannelName = (channel: { name?: string; scope?: string }) => {
    // Use the channel name from backend, but provide fallbacks for common scopes
    if (channel.name) return channel.name;
    
    switch (channel.scope) {
      case 'admins': return t(keys.collaborationHubs.chat.adminsOnly);
      case 'admins_reviewers': return t(keys.collaborationHubs.chat.contentReview);
      case 'creator_specific': return t(keys.collaborationHubs.chat.workspace);
      default: return t(keys.collaborationHubs.chat.general);
    }
  };

  const formatLastMessage = (message: { content?: string; attachments?: unknown[] }) => {
    if (!message) return '';
    
    // If message has attachments, show attachment indicator
    if (message.attachments && message.attachments.length > 0) {
      const attachmentCount = message.attachments.length;
      return `📎 ${attachmentCount} attachment${attachmentCount > 1 ? 's' : ''}`;
    }
    
    // Truncate long messages
    const content = message.content || '';
    return content.length > 50
      ? `${content.substring(0, 50)}...`
      : content;
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-2", className)}>
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-full" />
              <Skeleton className="h-3 w-2/3" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <div className="text-center space-y-2">
          <p className="text-sm text-destructive">{t(keys.collaborationHubs.chat.error)}</p>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            {t(keys.common.retry)}
          </Button>
        </div>
      </div>
    );
  }

  if (channels.length === 0) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-sm text-muted-foreground">No channels available</p>
      </div>
    );
  }

  return (
    <ScrollArea className={cn("", className)}>
      <div className="space-y-1">
        {channels.map((channel) => (
          <button
            key={channel.id}
            onClick={() => onChannelSelect(channel.id!)}
            className={cn(
              "w-full text-left p-3 rounded-lg transition-colors hover:bg-muted/50",
              selectedChannelId === channel.id && "bg-muted"
            )}
          >
            <div className="flex items-start gap-3">
              <div className="flex items-center gap-2 min-w-0 flex-1">
                {getChannelIcon(channel.scope || '')}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm truncate">
                      {getChannelName(channel)}
                    </span>
                    {(channel.unread_count || 0) > 0 && (
                      <Badge variant="destructive" className="text-xs px-1.5 py-0.5 min-w-[1.25rem] h-5">
                        {channel.unread_count! > 99 ? '99+' : channel.unread_count}
                      </Badge>
                    )}
                  </div>
                  {channel.last_message && (
                    <p className="text-xs text-muted-foreground line-clamp-1">
                      {formatLastMessage(channel.last_message)}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex flex-col items-end gap-1 text-xs text-muted-foreground">
                {channel.last_activity_at && (
                  <span>{formatTime(channel.last_activity_at)}</span>
                )}
                <span>{channel.participant_count || 0}</span>
              </div>
            </div>
          </button>
        ))}
      </div>
    </ScrollArea>
  );
}

export function ChannelListSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="p-3 space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-6 ml-auto" />
          </div>
          <Skeleton className="h-3 w-full" />
          <div className="flex justify-between">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-3 w-8" />
          </div>
        </div>
      ))}
    </div>
  );
}
