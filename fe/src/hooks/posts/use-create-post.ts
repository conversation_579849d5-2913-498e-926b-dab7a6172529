import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for creating a new post in a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the posts list query on success
 * to ensure the UI shows the newly created post.
 * 
 * The backend automatically:
 * - Scopes the post to the current account (multi-tenancy)
 * - Sets the creator to the current user
 * - Validates media URIs and post content
 * - <PERSON>les reviewer assignments
 */
export function useCreatePost() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/hubs/{hubId}/posts', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch posts list for this hub
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/posts', { params: { path: { hubId: variables.params.path.hubId } } }],
      });
      
      // Invalidate hub details to update stats
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.hubId } } }],
      });
    },
  });
}
