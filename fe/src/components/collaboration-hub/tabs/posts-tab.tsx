import { useState } from "react"
import { Plus, Search } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { PostFormDialog, PostsList } from "@/components/posts"
import { usePermissions } from "@/hooks/use-permissions"
import { useDebounce } from "@/lib/utils"
import { toast } from "sonner"

interface PostsTabProps {
  hubId: number
}

export function PostsTab({ hubId }: PostsTabProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [createDialogOpen, setCreateDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [editingPostId, setEditingPostId] = useState<number | null>(null)
  const { t, keys } = useTranslations()
  const { canCreatePost } = usePermissions()

  // Debounce search query to avoid excessive API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300)





  const handleCreatePost = () => {
    if (!canCreatePost()) {
      toast.error("You don't have permission to create posts")
      return
    }
    setCreateDialogOpen(true)
  }

  const handleEditPost = (postId: number) => {
    setEditingPostId(postId)
    setEditDialogOpen(true)
  }

  const handleDialogSuccess = () => {
    // Show success message
    if (editingPostId) {
      toast.success(t(keys.collaborationHubs.posts.postUpdated))
    } else {
      toast.success(t(keys.collaborationHubs.posts.postCreated))
    }
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with filters and actions */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t(keys.collaborationHubs.posts.searchPlaceholder)}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder={t(keys.collaborationHubs.posts.filterByStatus)} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t(keys.collaborationHubs.posts.allPosts)}</SelectItem>
              <SelectItem value="pending">{t(keys.collaborationHubs.posts.pendingReview)}</SelectItem>
              <SelectItem value="approved">{t(keys.collaborationHubs.posts.approved)}</SelectItem>
              <SelectItem value="rework">{t(keys.collaborationHubs.posts.needsRework)}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {canCreatePost() && (
          <Button onClick={handleCreatePost}>
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{t(keys.collaborationHubs.posts.createPost)}</span>
          </Button>
        )}
      </div>

      {/* Posts Content */}
      <PostsList
        hubId={hubId}
        searchQuery={debouncedSearchQuery}
        statusFilter={statusFilter}
        onEditPost={handleEditPost}
        className="flex-1"
      />

      {/* Create Post Dialog */}
      <PostFormDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        hubId={hubId}
        onSuccess={handleDialogSuccess}
      />

      {/* Edit Post Dialog */}
      <PostFormDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        hubId={hubId}
        postId={editingPostId}
        onSuccess={handleDialogSuccess}
      />
    </div>
  )
}
