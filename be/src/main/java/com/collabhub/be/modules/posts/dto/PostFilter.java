package com.collabhub.be.modules.posts.dto;

import java.util.Arrays;
import java.util.List;

/**
 * Enum representing available post filters.
 */
public enum PostFilter {
    ALL("all"),
    ASSIGNED_TO_ME("assigned_to_me"),
    CREATED_BY_ME("created_by_me"),
    NEEDS_REVIEW("needs_review");

    private final String value;

    PostFilter(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    /**
     * Returns all available filter values as strings.
     */
    public static List<String> getAllValues() {
        return Arrays.stream(PostFilter.values())
                .map(PostFilter::getValue)
                .toList();
    }

    /**
     * Creates a PostFilter from a string value.
     */
    public static PostFilter fromString(String value) {
        if (value == null) {
            return ALL; // Default fallback
        }
        
        for (PostFilter filter : PostFilter.values()) {
            if (filter.getValue().equalsIgnoreCase(value)) {
                return filter;
            }
        }
        
        return ALL; // Default fallback for unknown filters
    }

    @Override
    public String toString() {
        return value;
    }
}
