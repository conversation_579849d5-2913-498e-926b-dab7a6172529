package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Lightweight DTO for collaboration brief list items.
 * Used in paginated lists to avoid N+1 query problems.
 */
public class CollaborationBriefListItemDto {

    @NotNull
    private Long id;

    @NotNull
    private String title;

    private String bodyPreview; // First 200 characters of body

    @NotNull
    private Long createdByParticipantId;

    private String createdByParticipantName;

    @NotNull
    private BriefScopeDto scope;

    private List<Long> specificParticipantIds;

    @NotNull
    private LocalDateTime createdAt;

    @NotNull
    private LocalDateTime updatedAt;

    public CollaborationBriefListItemDto() {}

    public CollaborationBriefListItemDto(Long id, String title, String bodyPreview,
                                         Long createdByParticipantId, String createdByParticipantName,
                                         BriefScopeDto scope, List<Long> specificParticipantIds,
                                         LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.title = title;
        this.bodyPreview = bodyPreview;
        this.createdByParticipantId = createdByParticipantId;
        this.createdByParticipantName = createdByParticipantName;
        this.scope = scope;
        this.specificParticipantIds = specificParticipantIds;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBodyPreview() {
        return bodyPreview;
    }

    public void setBodyPreview(String bodyPreview) {
        this.bodyPreview = bodyPreview;
    }

    public Long getCreatedByParticipantId() {
        return createdByParticipantId;
    }

    public void setCreatedByParticipantId(Long createdByParticipantId) {
        this.createdByParticipantId = createdByParticipantId;
    }

    public String getCreatedByParticipantName() {
        return createdByParticipantName;
    }

    public void setCreatedByParticipantName(String createdByParticipantName) {
        this.createdByParticipantName = createdByParticipantName;
    }

    public BriefScopeDto getScope() {
        return scope;
    }

    public void setScope(BriefScopeDto scope) {
        this.scope = scope;
    }

    public List<Long> getSpecificParticipantIds() {
        return specificParticipantIds;
    }

    public void setSpecificParticipantIds(List<Long> specificParticipantIds) {
        this.specificParticipantIds = specificParticipantIds;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "CollaborationBriefListItemDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", bodyPreview='" + bodyPreview + '\'' +
                ", createdByParticipantId=" + createdByParticipantId +
                ", createdByParticipantName='" + createdByParticipantName + '\'' +
                ", scope=" + scope +
                ", specificParticipantIds=" + specificParticipantIds +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
