package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for collaboration brief details.
 * Contains complete brief information including metadata.
 */
public class CollaborationBriefResponse {

    @NotNull
    private Long id;

    @NotNull
    private Long hubId;

    @NotNull
    private String title;

    private String body;

    @NotNull
    private Long createdByParticipantId;

    private String createdByParticipantName;

    @NotNull
    private BriefScopeDto scope;

    private List<Long> specificParticipantIds;

    @NotNull
    private LocalDateTime createdAt;

    @NotNull
    private LocalDateTime updatedAt;

    public CollaborationBriefResponse() {}

    public CollaborationBriefResponse(Long id, Long hubId, String title, String body,
                                      Long createdByParticipantId, String createdByParticipantName,
                                      BriefScopeDto scope, List<Long> specificParticipantIds,
                                      LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.hubId = hubId;
        this.title = title;
        this.body = body;
        this.createdByParticipantId = createdByParticipantId;
        this.createdByParticipantName = createdByParticipantName;
        this.scope = scope;
        this.specificParticipantIds = specificParticipantIds;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHubId() {
        return hubId;
    }

    public void setHubId(Long hubId) {
        this.hubId = hubId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Long getCreatedByParticipantId() {
        return createdByParticipantId;
    }

    public void setCreatedByParticipantId(Long createdByParticipantId) {
        this.createdByParticipantId = createdByParticipantId;
    }

    public String getCreatedByParticipantName() {
        return createdByParticipantName;
    }

    public void setCreatedByParticipantName(String createdByParticipantName) {
        this.createdByParticipantName = createdByParticipantName;
    }

    public BriefScopeDto getScope() {
        return scope;
    }

    public void setScope(BriefScopeDto scope) {
        this.scope = scope;
    }

    public List<Long> getSpecificParticipantIds() {
        return specificParticipantIds;
    }

    public void setSpecificParticipantIds(List<Long> specificParticipantIds) {
        this.specificParticipantIds = specificParticipantIds;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "CollaborationBriefResponse{" +
                "id=" + id +
                ", hubId=" + hubId +
                ", title='" + title + '\'' +
                ", body='" + (body != null ? body.substring(0, Math.min(body.length(), 50)) + "..." : null) + '\'' +
                ", createdByParticipantId=" + createdByParticipantId +
                ", createdByParticipantName='" + createdByParticipantName + '\'' +
                ", scope=" + scope +
                ", specificParticipantIds=" + specificParticipantIds +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
