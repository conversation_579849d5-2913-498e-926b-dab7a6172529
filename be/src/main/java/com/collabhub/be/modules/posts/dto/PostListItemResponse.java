package com.collabhub.be.modules.posts.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.jooq.generated.enums.ReviewStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Lightweight response DTO for post list items.
 * Contains only essential information for list views to avoid N+1 queries.
 */
public class PostListItemResponse {

    private Long id;
    private String caption;

    @JsonProperty("media_count")
    private int mediaCount;

    @JsonProperty("media_uris")
    private List<MediaItem> mediaUris;

    @JsonProperty("review_status")
    private ReviewStatus reviewStatus;

    @JsonProperty("reviewer_notes")
    private String reviewerNotes;

    private PostCreator creator;

    @JsonProperty("my_review_status")
    private ReviewStatus myReviewStatus;

    @JsonProperty("comment_count")
    private int commentCount;

    @JsonProperty("assigned_reviewers")
    private List<PostReviewer> assignedReviewers;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("can_edit")
    private boolean canEdit;

    @JsonProperty("can_review")
    private boolean canReview;

    @JsonProperty("can_comment")
    private boolean canComment;

    public PostListItemResponse() {}

    public PostListItemResponse(Long id, String caption, int mediaCount, List<MediaItem> mediaUris,
                               ReviewStatus reviewStatus, String reviewerNotes, PostCreator creator, ReviewStatus myReviewStatus,
                               int commentCount, List<PostReviewer> assignedReviewers,
                               LocalDateTime createdAt, boolean canEdit, boolean canReview, boolean canComment) {
        this.id = id;
        this.caption = caption;
        this.mediaCount = mediaCount;
        this.mediaUris = mediaUris != null ? mediaUris : List.of();
        this.reviewStatus = reviewStatus;
        this.reviewerNotes = reviewerNotes;
        this.creator = creator;
        this.myReviewStatus = myReviewStatus;
        this.commentCount = commentCount;
        this.assignedReviewers = assignedReviewers != null ? assignedReviewers : List.of();
        this.createdAt = createdAt;
        this.canEdit = canEdit;
        this.canReview = canReview;
        this.canComment = canComment;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaption() {
        return caption;
    }

    public void setCaption(String caption) {
        this.caption = caption;
    }

    public int getMediaCount() {
        return mediaCount;
    }

    public void setMediaCount(int mediaCount) {
        this.mediaCount = mediaCount;
    }

    public List<MediaItem> getMediaUris() {
        return mediaUris;
    }

    public void setMediaUris(List<MediaItem> mediaUris) {
        this.mediaUris = mediaUris;
    }

    public ReviewStatus getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(ReviewStatus reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewerNotes() {
        return reviewerNotes;
    }

    public void setReviewerNotes(String reviewerNotes) {
        this.reviewerNotes = reviewerNotes;
    }

    public PostCreator getCreator() {
        return creator;
    }

    public void setCreator(PostCreator creator) {
        this.creator = creator;
    }

    public ReviewStatus getMyReviewStatus() {
        return myReviewStatus;
    }

    public void setMyReviewStatus(ReviewStatus myReviewStatus) {
        this.myReviewStatus = myReviewStatus;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public List<PostReviewer> getAssignedReviewers() {
        return assignedReviewers;
    }

    public void setAssignedReviewers(List<PostReviewer> assignedReviewers) {
        this.assignedReviewers = assignedReviewers;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public boolean isCanEdit() {
        return canEdit;
    }

    public void setCanEdit(boolean canEdit) {
        this.canEdit = canEdit;
    }

    public boolean isCanReview() {
        return canReview;
    }

    public void setCanReview(boolean canReview) {
        this.canReview = canReview;
    }

    public boolean isCanComment() {
        return canComment;
    }

    public void setCanComment(boolean canComment) {
        this.canComment = canComment;
    }

    /**
     * Nested class representing the post creator.
     */
    public static class PostCreator {
        private Long id;
        private String name;
        private String email;

        public PostCreator() {}

        public PostCreator(Long id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }

    /**
     * Nested class representing a post reviewer for list items.
     */
    public static class PostReviewer {
        private Long id;
        private String name;
        private String email;
        private ReviewStatus status;

        public PostReviewer() {}

        public PostReviewer(Long id, String name, String email, ReviewStatus status) {
            this.id = id;
            this.name = name;
            this.email = email;
            this.status = status;
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public ReviewStatus getStatus() {
            return status;
        }

        public void setStatus(ReviewStatus status) {
            this.status = status;
        }
    }

    @Override
    public String toString() {
        return "PostListItemResponse{" +
                "id=" + id +
                ", caption='" + caption + '\'' +
                ", mediaCount=" + mediaCount +
                ", mediaUris=" + mediaUris +
                ", reviewStatus=" + reviewStatus +
                ", reviewerNotes='" + reviewerNotes + '\'' +
                ", creator=" + creator +
                ", myReviewStatus=" + myReviewStatus +
                ", commentCount=" + commentCount +
                ", assignedReviewers=" + assignedReviewers +
                ", createdAt=" + createdAt +
                ", canEdit=" + canEdit +
                ", canReview=" + canReview +
                ", canComment=" + canComment +
                '}';
    }
}
