package com.collabhub.be.modules.collaborationhub.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * Request DTO for updating an existing collaboration brief.
 * Contains the fields that can be updated for a brief.
 */
public class CollaborationBriefUpdateRequest {

    @NotBlank(message = "Brief title is required")
    @Size(max = 255, message = "Brief title must not exceed 255 characters")
    private String title;

    @Size(max = 10000, message = "Brief body must not exceed 10000 characters")
    private String body;

    @NotNull(message = "Brief scope is required")
    private BriefScopeDto scope;

    private List<Long> specificParticipantIds;

    public CollaborationBriefUpdateRequest() {}

    public CollaborationBriefUpdateRequest(String title, String body, BriefScopeDto scope, List<Long> specificParticipantIds) {
        this.title = title;
        this.body = body;
        this.scope = scope;
        this.specificParticipantIds = specificParticipantIds;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public BriefScopeDto getScope() {
        return scope;
    }

    public void setScope(BriefScopeDto scope) {
        this.scope = scope;
    }

    public List<Long> getSpecificParticipantIds() {
        return specificParticipantIds;
    }

    public void setSpecificParticipantIds(List<Long> specificParticipantIds) {
        this.specificParticipantIds = specificParticipantIds;
    }

    @Override
    public String toString() {
        return "CollaborationBriefUpdateRequest{" +
                "title='" + title + '\'' +
                ", body='" + (body != null ? body.substring(0, Math.min(body.length(), 50)) + "..." : null) + '\'' +
                '}';
    }
}
