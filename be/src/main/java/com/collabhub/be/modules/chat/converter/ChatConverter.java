package com.collabhub.be.modules.chat.converter;

import com.collabhub.be.modules.auth.repository.UserRepository;
import com.collabhub.be.modules.chat.dto.*;
import com.collabhub.be.modules.media.converter.MediaConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.JSONB;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.ChatChannel;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.User;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Converter for transforming chat entities to/from DTOs.
 */
@Component
public class ChatConverter {

    private static final Logger logger = LoggerFactory.getLogger(ChatConverter.class);
    private final ObjectMapper objectMapper;
    private final UserRepository userRepository;
    private final MediaConverter mediaConverter;

    public ChatConverter(ObjectMapper objectMapper, UserRepository userRepository, MediaConverter mediaConverter) {
        this.objectMapper = objectMapper;
        this.userRepository = userRepository;
        this.mediaConverter = mediaConverter;
    }

    /**
     * Converts ChatChannel entity to response DTO.
     */
    public ChatChannelResponse toChannelResponse(ChatChannel channel, Long participantCount, Long unreadCount,
                                               ChatMessageResponse lastMessage, Boolean canAccess, Boolean canWrite) {
        if (channel == null) {
            return null;
        }

        String description = generateChannelDescription(channel);

        return new ChatChannelResponse(
                channel.getId(),
                channel.getName(),
                description,
                channel.getScope(),
                channel.getCreatorParticipantId(),
                participantCount,
                unreadCount,
                lastMessage,
                canAccess,
                canWrite,
                channel.getCreatedAt(),
                channel.getLastActivityAt()
        );
    }

    /**
     * Converts ChatMessage entity to response DTO with media from junction table.
     */
    public ChatMessageResponse toMessageResponse(ChatMessage message, List<Media> mediaList, ChatParticipantDto sender) {
        if (message == null) {
            return null;
        }

        List<MentionDto> mentions = parseMentionsFromJsonb(message.getMentions());
        List<AttachmentDto> attachments = convertMediaToAttachments(mediaList);

        return new ChatMessageResponse(
                message.getId(),
                message.getContent(),
                sender,
                mentions,
                attachments,
                message.getCreatedAt(),
                message.getUpdatedAt(),
                message.getEditedAt()
        );
    }



    /**
     * Converts HubParticipant to ChatParticipantDto.
     */
    public ChatParticipantDto toParticipantDto(HubParticipant participant) {
        if (participant == null) {
            return null;
        }

        String displayName = getParticipantDisplayName(participant);

        return new ChatParticipantDto(
                participant.getId(),
                displayName,
                participant.getEmail(),
                participant.getRole(),
                participant.getIsExternal()
        );
    }



    /**
     * Converts list of mentions to JSONB for database storage.
     */
    public JSONB mentionsToJsonb(List<MentionDto> mentions) {
        if (mentions == null || mentions.isEmpty()) {
            return JSONB.valueOf("[]");
        }

        try {
            String json = objectMapper.writeValueAsString(mentions);
            return JSONB.valueOf(json);
        } catch (JsonProcessingException e) {
            logger.error("Failed to convert mentions to JSONB", e);
            return JSONB.valueOf("[]");
        }
    }

    /**
     * Parses mentions from JSONB database field.
     */
    private List<MentionDto> parseMentionsFromJsonb(JSONB mentionsJsonb) {
        if (mentionsJsonb == null) {
            return new ArrayList<>();
        }

        try {
            String json = mentionsJsonb.data();
            return objectMapper.readValue(json, new TypeReference<List<MentionDto>>() {});
        } catch (JsonProcessingException e) {
            logger.error("Failed to parse mentions from JSONB: {}", mentionsJsonb.data(), e);
            return new ArrayList<>();
        }
    }



    /**
     * Generates a description for a chat channel based on its scope.
     */
    private String generateChannelDescription(ChatChannel channel) {
        return switch (channel.getScope()) {
            case admins -> "Private channel for administrators";
            case admins_reviewers -> "Channel for administrators and reviewers";
            case creator_specific -> "Private channel for content creator collaboration";
        };
    }

    /**
     * Gets display name for a participant.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant.getUserId() != null) {
            // For internal users, fetch from user service
            User user = userRepository.findById(participant.getUserId());
            if (user != null && user.getDisplayName() != null) {
                return user.getDisplayName();
            }
            // Fallback to email prefix if user not found or name is null
            return participant.getEmail() != null ?
                   participant.getEmail().substring(0, participant.getEmail().indexOf('@')) :
                   "Unknown User";
        } else {
            // For external users, use name if available, otherwise email prefix
            if (participant.getName() != null) {
                return participant.getName();
            }
            return participant.getEmail() != null ?
                   participant.getEmail().substring(0, participant.getEmail().indexOf('@')) :
                   "External User";
        }
    }

    /**
     * Extracts filename from URI.
     */
    private String extractFilenameFromUri(String uri) {
        if (uri == null) return "unknown";
        int lastSlash = uri.lastIndexOf('/');
        return lastSlash >= 0 ? uri.substring(lastSlash + 1) : uri;
    }

    /**
     * Determines file type from URI extension.
     */
    private String determineFileType(String uri) {
        if (uri == null) return "unknown";
        String filename = extractFilenameFromUri(uri).toLowerCase();

        if (filename.matches(".*\\.(jpg|jpeg|png|gif|webp)$")) {
            return "image";
        } else if (filename.matches(".*\\.(mp4|avi|mov|wmv|webm)$")) {
            return "video";
        } else if (filename.matches(".*\\.(pdf|doc|docx|txt|rtf)$")) {
            return "document";
        } else {
            return "file";
        }
    }

    /**
     * Determines content type (MIME type) from URI extension.
     */
    private String determineContentType(String uri) {
        if (uri == null) return "application/octet-stream";
        String filename = extractFilenameFromUri(uri).toLowerCase();

        if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (filename.endsWith(".png")) {
            return "image/png";
        } else if (filename.endsWith(".gif")) {
            return "image/gif";
        } else if (filename.endsWith(".webp")) {
            return "image/webp";
        } else if (filename.endsWith(".mp4")) {
            return "video/mp4";
        } else if (filename.endsWith(".mov")) {
            return "video/quicktime";
        } else if (filename.endsWith(".avi")) {
            return "video/x-msvideo";
        } else if (filename.endsWith(".wmv")) {
            return "video/x-ms-wmv";
        } else if (filename.endsWith(".webm")) {
            return "video/webm";
        } else if (filename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (filename.endsWith(".doc")) {
            return "application/msword";
        } else if (filename.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (filename.endsWith(".txt")) {
            return "text/plain";
        } else if (filename.endsWith(".rtf")) {
            return "application/rtf";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Converts Media entities to AttachmentDto list for API compatibility.
     */
    private List<AttachmentDto> convertMediaToAttachments(List<Media> mediaList) {
        if (mediaList == null || mediaList.isEmpty()) {
            return new ArrayList<>();
        }

        return mediaList.stream()
                .map(media -> new AttachmentDto(
                        generateMediaUrl(media),
                        media.getOriginalFilename(),
                        media.getMimeType(),
                        media.getFileSizeBytes(),
                        determineFileTypeFromMimeType(media.getMimeType())
                ))
                .toList();
    }

    /**
     * Generates URL for media file.
     */
    private String generateMediaUrl(Media media) {
        // Use MediaConverter to generate URL
        return mediaConverter.toDto(media).getUrl();
    }

    /**
     * Determines file type from MIME type.
     */
    private String determineFileTypeFromMimeType(String mimeType) {
        if (mimeType == null) return "file";

        if (mimeType.startsWith("image/")) {
            return "image";
        } else if (mimeType.startsWith("video/")) {
            return "video";
        } else if (mimeType.equals("application/pdf") ||
                   mimeType.contains("document") ||
                   mimeType.equals("text/plain")) {
            return "document";
        } else {
            return "file";
        }
    }
}
