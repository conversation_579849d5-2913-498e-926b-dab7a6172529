import { useAuth } from '@/contexts/auth-context'
import { Permission } from '@/lib/types/api'

/**
 * Custom hook for checking user permissions and roles.
 * Provides utility functions to check if the current user has specific permissions.
 */
export function usePermissions() {
  const { user, hasPermission, hasAnyPermission } = useAuth()

  // ============================================================================
  // ENTITY PERMISSIONS
  // ============================================================================

  /**
   * Check if the current user can view brands.
   */
  const canViewBrands = () => {
    return hasPermission(Permission.BRAND_READ)
  }

  /**
   * Check if the current user can create/edit brands.
   */
  const canManageBrands = () => {
    return hasPermission(Permission.BRAND_WRITE)
  }

  /**
   * Check if the current user can delete brands.
   */
  const canDeleteBrands = () => {
    return hasPermission(Permission.BRAND_DELETE)
  }

  /**
   * Check if the current user can view collaboration hubs.
   */
  const canViewHubs = () => {
    return hasPermission(Permission.HUB_READ)
  }

  /**
   * Check if the current user can create/edit collaboration hubs.
   */
  const canManageHubs = () => {
    return hasPermission(Permission.HUB_WRITE)
  }

  /**
   * Check if the current user can delete collaboration hubs.
   */
  const canDeleteHubs = () => {
    return hasPermission(Permission.HUB_DELETE)
  }

  /**
   * Check if the current user can view invoices.
   */
  const canViewInvoices = () => {
    return hasPermission(Permission.INVOICE_READ)
  }

  /**
   * Check if the current user can create/edit invoices.
   */
  const canManageInvoices = () => {
    return hasPermission(Permission.INVOICE_WRITE)
  }

  /**
   * Check if the current user can delete invoices.
   */
  const canDeleteInvoices = () => {
    return hasPermission(Permission.INVOICE_DELETE)
  }

  /**
   * Check if the current user can view account companies.
   */
  const canViewCompanies = () => {
    return hasPermission(Permission.COMPANY_READ)
  }

  /**
   * Check if the current user can create/edit account companies.
   */
  const canManageCompanies = () => {
    return hasPermission(Permission.COMPANY_WRITE)
  }

  /**
   * Check if the current user can view bank details.
   */
  const canViewBankDetails = () => {
    return hasPermission(Permission.BANK_READ)
  }

  /**
   * Check if the current user can create/edit bank details.
   */
  const canManageBankDetails = () => {
    return hasPermission(Permission.BANK_WRITE)
  }

  // ============================================================================
  // POST PERMISSIONS
  // ============================================================================

  /**
   * Check if the current user has permission to create posts.
   */
  const canCreatePost = () => {
    return hasPermission(Permission.POST_WRITE)
  }

  /**
   * Check if the current user can edit a specific post.
   * This is a simplified check - actual post editing permissions
   * are handled by the backend based on ownership and role.
   */
  const canEditPost = (postCreatorId?: number) => {
    if (!user) return false

    // Check if user has post update permission
    if (!hasPermission(Permission.POST_UPDATE)) return false

    // Admin can edit any post
    if (user.role === 'ADMIN') return true

    // Regular users can only edit their own posts
    if (postCreatorId) {
      return user.id === postCreatorId
    }

    return false
  }

  /**
   * Check if the current user can delete a specific post.
   * Same rules as editing - ADMIN can delete any post, USER can only delete their own.
   */
  const canDeletePost = (postCreatorId?: number) => {
    if (!user) return false

    // Check if user has post delete permission
    if (!hasPermission(Permission.POST_DELETE)) return false

    // Admin can delete any post
    if (user.role === 'ADMIN') return true

    // Regular users can only delete their own posts
    if (postCreatorId) {
      return user.id === postCreatorId
    }

    return false
  }

  // ============================================================================
  // ROLE HELPERS (for backward compatibility)
  // ============================================================================

  /**
   * Check if the current user is an admin.
   */
  const isAdmin = () => {
    return user?.role === 'ADMIN'
  }

  /**
   * Check if the current user is a regular user.
   */
  const isUser = () => {
    return user?.role === 'USER'
  }

  /**
   * Check if the current user is an external participant.
   */
  const isExternalParticipant = () => {
    return user?.role === 'EXTERNAL_PARTICIPANT'
  }

  /**
   * Get the current user's role.
   */
  const getUserRole = () => {
    return user?.role
  }

  /**
   * Get the current user's ID.
   */
  const getUserId = () => {
    return user?.id
  }

  return {
    // Entity permissions
    canViewBrands,
    canManageBrands,
    canDeleteBrands,
    canViewHubs,
    canManageHubs,
    canDeleteHubs,
    canViewInvoices,
    canManageInvoices,
    canDeleteInvoices,
    canViewCompanies,
    canManageCompanies,
    canViewBankDetails,
    canManageBankDetails,

    // Post permissions
    canCreatePost,
    canEditPost,
    canDeletePost,

    // Role helpers
    isAdmin,
    isUser,
    isExternalParticipant,
    getUserRole,
    getUserId,

    // Direct permission checking
    hasPermission,
    hasAnyPermission,

    // User object
    user
  }
}
