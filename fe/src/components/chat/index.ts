/**
 * Chat components for the Collaboration Hub frontend.
 *
 * These components provide a comprehensive real-time chat interface
 * with WebSocket support, file attachments, mentions, and message editing.
 *
 * Main Components:
 * - MessageList: Message display with infinite scroll and real-time updates
 * - MessageInput: Message composition with file upload and mentions
 * - MessageItem: Individual message display with edit/delete actions
 * - ChannelList: Channel sidebar with unread counts and last messages
 *
 * All components follow established patterns:
 * - Mobile-responsive design using useIsMobile hook
 * - Real-time updates via WebSocket integration
 * - Proper loading states and error handling
 * - Integration with openapi-react-query hooks
 * - Consistent styling with shadcn/ui components
 * - File upload with progress tracking
 * - Message editing and deletion with permissions
 */

export { MessageList } from './message-list';
export { MessageInput } from './message-input';
export { MessageItem } from './message-item';
export { ChannelList, ChannelListSkeleton } from './channel-list';
export { ChannelMembersModal } from './channel-members-modal';
