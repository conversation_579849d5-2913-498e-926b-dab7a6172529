package com.collabhub.be.modules.chat.repository;

import org.jooq.DSLContext;
import org.jooq.generated.enums.ChatChannelScope;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.daos.ChatChannelDao;
import org.jooq.generated.tables.pojos.ChatChannel;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.jooq.generated.Tables.CHAT_MESSAGE;
import static org.jooq.generated.tables.ChatChannel.CHAT_CHANNEL;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;

/**
 * Repository for ChatChannel entity using jOOQ for database operations.
 * Provides type-safe database operations for chat channel management.
 */
@Repository
public class ChatChannelRepositoryImpl extends ChatChannelDao {

    private final DSLContext dsl;

    public ChatChannelRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds all chat channels for a hub that are accessible by the given participant.
     * 
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @return list of accessible chat channels
     */
    public List<ChatChannel> findAccessibleChannelsByHubAndParticipant(Long hubId, Long participantId, 
                                                                       HubParticipantRole participantRole) {
        var query = dsl.select()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.HUB_ID.eq(hubId));

        // Apply role-based filtering
        switch (participantRole) {
            case admin:
                // Admins can access all channels
                break;
            case reviewer, reviewer_creator:
                // Reviewers can access admins_reviewers and creator_specific channels
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific))
                );
                break;
            case content_creator:
                // Content creators can access admins_reviewers and their own creator_specific channel
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific)
                        .and(CHAT_CHANNEL.CREATOR_PARTICIPANT_ID.eq(participantId)))
                );
                break;
        }

        return query.orderBy(CHAT_CHANNEL.LAST_ACTIVITY_AT.desc().nullsLast())
                .fetchInto(ChatChannel.class);
    }

    /**
     * Finds a chat channel by ID and verifies the participant has access to it.
     * 
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @return optional chat channel if accessible
     */
    public Optional<ChatChannel> findAccessibleChannelById(Long channelId, Long participantId, 
                                                          HubParticipantRole participantRole) {
        var query = dsl.select()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.ID.eq(channelId));

        // Apply role-based filtering
        switch (participantRole) {
            case admin:
                // Admins can access all channels
                break;
            case reviewer, reviewer_creator:
                // Reviewers can access admins_reviewers and creator_specific channels
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific))
                );
                break;
            case content_creator:
                // Content creators can access admins_reviewers and their own creator_specific channel
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific)
                        .and(CHAT_CHANNEL.CREATOR_PARTICIPANT_ID.eq(participantId)))
                );
                break;
        }

        return query.fetchOptionalInto(ChatChannel.class);
    }

    /**
     * Creates default chat channels for a new collaboration hub.
     * 
     * @param hubId the hub ID
     * @return list of created channels
     */
    public List<ChatChannel> createDefaultChannels(Long hubId) {
        LocalDateTime now = LocalDateTime.now();
        
        // Create admins-only channel
        ChatChannel adminsChannel = new ChatChannel();
        adminsChannel.setHubId(hubId);
        adminsChannel.setName("Admins Only");
        adminsChannel.setScope(ChatChannelScope.admins);
        adminsChannel.setCreatedAt(now);
        adminsChannel.setLastActivityAt(now);
        insert(adminsChannel);

        // Create admins + reviewers channel
        ChatChannel reviewersChannel = new ChatChannel();
        reviewersChannel.setHubId(hubId);
        reviewersChannel.setName("Admins & Reviewers");
        reviewersChannel.setScope(ChatChannelScope.admins_reviewers);
        reviewersChannel.setCreatedAt(now);
        reviewersChannel.setLastActivityAt(now);
        insert(reviewersChannel);

        return List.of(adminsChannel, reviewersChannel);
    }

    /**
     * Creates a creator-specific channel for a content creator.
     * 
     * @param hubId the hub ID
     * @param creatorParticipantId the creator participant ID
     * @param creatorName the creator's display name
     * @return the created channel
     */
    public ChatChannel createCreatorChannel(Long hubId, Long creatorParticipantId, String creatorName) {
        LocalDateTime now = LocalDateTime.now();
        
        ChatChannel creatorChannel = new ChatChannel();
        creatorChannel.setHubId(hubId);
        creatorChannel.setName("Creator: " + creatorName);
        creatorChannel.setScope(ChatChannelScope.creator_specific);
        creatorChannel.setCreatorParticipantId(creatorParticipantId);
        creatorChannel.setCreatedAt(now);
        creatorChannel.setLastActivityAt(now);
        insert(creatorChannel);

        return creatorChannel;
    }

    /**
     * Updates the last activity timestamp for a channel.
     * This will be called from the application layer when messages are sent.
     * 
     * @param channelId the channel ID
     */
    public void updateLastActivity(Long channelId) {
        dsl.update(CHAT_CHANNEL)
                .set(CHAT_CHANNEL.LAST_ACTIVITY_AT, LocalDateTime.now())
                .where(CHAT_CHANNEL.ID.eq(channelId))
                .execute();
    }

    /**
     * Counts unread messages for a participant in a channel.
     * This is a simplified implementation that counts messages created after
     * the participant's last activity in the hub.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return unread message count
     */
    public Long countUnreadMessages(Long channelId, Long participantId) {
        // Simplified implementation: count all messages in the channel
        // excluding the participant's own messages
        // In a full implementation, you'd track read receipts in a separate table

        return dsl.selectCount()
                .from(CHAT_MESSAGE)
                .where(CHAT_MESSAGE.CHANNEL_ID.eq(channelId))
                .and(CHAT_MESSAGE.PARTICIPANT_ID.ne(participantId)) // Don't count own messages
                .fetchOne(0, Long.class);
    }

    /**
     * Counts participants who have access to a channel.
     * 
     * @param channelId the channel ID
     * @return participant count
     */
    public Long countChannelParticipants(Long channelId) {
        ChatChannel channel = fetchOneById(channelId);
        if (channel == null) {
            return 0L;
        }

        var query = dsl.selectCount()
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(channel.getHubId()))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull());

        // Apply scope-based filtering
        switch (channel.getScope()) {
            case admins:
                query = query.and(HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.admin));
                break;
            case admins_reviewers:
                query = query.and(
                    HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.admin)
                    .or(HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.reviewer))
                    .or(HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.reviewer_creator))
                );
                break;
            case creator_specific:
                query = query.and(
                    HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.admin)
                    .or(HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.reviewer))
                    .or(HUB_PARTICIPANT.ROLE.eq(HubParticipantRole.reviewer_creator))
                    .or(HUB_PARTICIPANT.ID.eq(channel.getCreatorParticipantId()))
                );
                break;
        }

        return query.fetchOne(0, Long.class);
    }

    /**
     * Finds accessible chat channels with pagination support.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @param pageRequest the pagination request
     * @return list of accessible chat channels
     */
    public List<ChatChannel> findAccessibleChannelsByHubAndParticipantWithPagination(Long hubId, Long participantId,
                                                                                    HubParticipantRole participantRole,
                                                                                    PageRequest pageRequest) {
        var query = dsl.select()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.HUB_ID.eq(hubId));

        // Apply role-based filtering
        switch (participantRole) {
            case admin:
                // Admins can access all channels
                break;
            case reviewer, reviewer_creator:
                // Reviewers can access admins_reviewers and creator_specific channels
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific))
                );
                break;
            case content_creator:
                // Content creators can access admins_reviewers and their own creator_specific channel
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific)
                        .and(CHAT_CHANNEL.CREATOR_PARTICIPANT_ID.eq(participantId)))
                );
                break;
        }

        return query.orderBy(CHAT_CHANNEL.LAST_ACTIVITY_AT.desc().nullsLast())
                .limit(pageRequest.getSize())
                .offset(pageRequest.getOffset())
                .fetchInto(ChatChannel.class);
    }

    /**
     * Counts accessible chat channels for a participant.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param participantRole the participant's role
     * @return count of accessible channels
     */
    public long countAccessibleChannelsByHubAndParticipant(Long hubId, Long participantId,
                                                          HubParticipantRole participantRole) {
        var query = dsl.selectCount()
                .from(CHAT_CHANNEL)
                .where(CHAT_CHANNEL.HUB_ID.eq(hubId));

        // Apply role-based filtering
        switch (participantRole) {
            case admin:
                // Admins can access all channels
                break;
            case reviewer, reviewer_creator:
                // Reviewers can access admins_reviewers and creator_specific channels
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific))
                );
                break;
            case content_creator:
                // Content creators can access admins_reviewers and their own creator_specific channel
                query = query.and(
                    CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.admins_reviewers)
                    .or(CHAT_CHANNEL.SCOPE.eq(ChatChannelScope.creator_specific)
                        .and(CHAT_CHANNEL.CREATOR_PARTICIPANT_ID.eq(participantId)))
                );
                break;
        }

        return query.fetchOne(0, Long.class);
    }
}
