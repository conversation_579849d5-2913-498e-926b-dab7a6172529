import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Calendar, Building2, Eye, Pencil, Trash2, ExternalLink } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { useNavigate } from "react-router"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { usePermissions } from "@/hooks/use-permissions"
import { ROUTES } from "@/router/routes"
import type { CollaborationHubListItemDto } from "./types"

interface CollaborationHubCardProps {
  hub: CollaborationHubListItemDto
  onEdit?: (id: number) => void
  onDelete?: (id: number) => void
}

export function CollaborationHubCard({ hub, onEdit, onDelete }: CollaborationHubCardProps) {
  const navigate = useNavigate()
  const { t, keys } = useTranslations()
  const { canManageHubs, canDeleteHubs } = usePermissions()

  // Check if user has any management permissions for this hub
  const canEdit = canManageHubs()
  const canDelete = canDeleteHubs()

  const getRoleBadgeVariant = (role?: string) => {
    if (!role) return 'secondary'
    switch (role.toLowerCase()) {
      case 'admin':
        return 'default'
      case 'content_creator':
        return 'secondary'
      case 'reviewer':
        return 'outline'
      case 'reviewer_creator':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const formatRole = (role?: string) => {
    if (!role) return 'Unknown'
    return t(keys.collaborationHubs.roles[role as keyof typeof keys.collaborationHubs.roles]) || role
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown'
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  }

  const handleViewHub = () => {
    if (hub.id) {
      navigate(ROUTES.COLLABORATION_HUB_DETAIL(hub.id))
    }
  }

  const handleEdit = () => {
    if (hub.id) {
      onEdit?.(hub.id)
    }
  }

  const handleDelete = () => {
    if (hub.id) {
      onDelete?.(hub.id)
    }
  }

  // Early return if essential data is missing
  if (!hub.id || !hub.name) {
    return null
  }

  return (
    <Card className="w-full hover:shadow-lg transition-all duration-200 relative cursor-pointer group" onClick={handleViewHub}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-lg leading-tight group-hover:text-primary transition-colors">{hub.name}</h3>
              {hub.brandName && (
                <p className="text-sm text-muted-foreground">{hub.brandName}</p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Open Button - always visible */}
            <Button
              variant="outline"
              size="sm"
              className="transition-opacity"
              onClick={(e) => {
                e.stopPropagation()
                handleViewHub()
              }}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              {t(keys.collaborationHubs.open)}
            </Button>

            {/* Actions Menu - show if user has any actions available */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 transition-opacity"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">More options</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleViewHub(); }}>
                  <Eye className="h-4 w-4 mr-2" />
                  {t(keys.collaborationHubs.viewHub)}
                </DropdownMenuItem>
                {canEdit && (
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleEdit(); }}>
                    <Pencil className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.editHub)}
                  </DropdownMenuItem>
                )}
                {canDelete && (
                  <DropdownMenuItem
                    onClick={(e) => { e.stopPropagation(); handleDelete(); }}
                    className="text-destructive focus:text-destructive focus:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.deleteHub)}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Role Badge */}
        <div className="flex items-center gap-2">
          <Badge variant={getRoleBadgeVariant(hub.myRole)}>
            {formatRole(hub.myRole)}
          </Badge>
        </div>

        {/* Hub Information */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span className="font-medium">{t(keys.collaborationHubs.createdAt)}:</span>
            <span>{formatDate(hub.createdAt)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
